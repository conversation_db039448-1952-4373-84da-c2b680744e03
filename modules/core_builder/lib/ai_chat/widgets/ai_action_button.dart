import 'dart:convert';

import 'package:feature_builder/constants/index.dart';
import 'package:feature_builder/route.dart';
import 'package:feature_builder/viewmodels/pages/index.dart';
import 'package:feature_builder/views/pages/index.dart';
import 'package:provider/provider.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../core_builder.dart';
import '../../example/environment/index.dart';
import '../../fluxstore/ui/module_ui.dart';
import '../providers/ai_chat_provider.dart';
import '../providers/chat_state_manager.dart';

enum ActionType {
  navigate,
  updateEnv,
  openUrl,
  exportConfig,
  submitBuildIOS,
  submitBuildAPK,
  submitBuildAAB,
  submitBuildPWA,
  submitBuildTestAPK,
  submitBuildTestIOS,
  retry,
  ;

  const ActionType();

  static ActionType? fromString(String? value) {
    try {
      return ActionType.values.byName('$value');
    } catch (e) {
      return null;
    }
  }
}

class AIActionButton extends StatelessWidget {
  const AIActionButton({super.key, this.action});

  final Map? action;

  @override
  Widget build(BuildContext context) {
    final actionType = ActionType.fromString(action?['type']);

    switch (actionType) {
      case ActionType.navigate:
        final path = action?['data'];
        final title = action?['title'];

        if (path is String &&
            path.isNotEmpty &&
            TypeFeature.values.indexWhere((element) => element.name == path) !=
                -1) {
          return OutlinedButton.icon(
            style: OutlinedButton.styleFrom(
              backgroundColor:
                  Theme.of(context).colorScheme.surfaceContainerHighest,
            ),
            onPressed: () {
              context.read<AIChatProvider>().hide();
              context.read<MenuModel>().screen = ScreenValue.feature;
              FeaturesConfigRoutes.navigatorToFeature(path);
            },
            label: Text(
              title ?? 'Click to navigate',
              style: TextStyle(color: Theme.of(context).colorScheme.primary),
            ),
            icon: const Icon(
              Icons.open_in_new,
              size: 16,
            ),
          );
        }
      case ActionType.openUrl:
        final url = action?['data'];
        final title = action?['title'];
        final uri = Uri.tryParse(url);

        if (uri != null) {
          return OutlinedButton.icon(
            style: OutlinedButton.styleFrom(
              backgroundColor:
                  Theme.of(context).colorScheme.surfaceContainerHighest,
            ),
            onPressed: () {
              launchUrl(uri);
            },
            label: Text(title ?? 'Click to open url'),
            icon: const Icon(
              Icons.link,
              size: 16,
            ),
          );
        }
      case ActionType.updateEnv:
        final dataEnv = jsonDecode('${action?['data']}');
        final title = action?['title'];
        if (dataEnv is Map && dataEnv.isNotEmpty) {
          return OutlinedButton.icon(
            style: OutlinedButton.styleFrom(
              backgroundColor:
                  Theme.of(context).colorScheme.surfaceContainerHighest,
            ),
            onPressed: () {
              final model = context.read<BuilderModel>();
              var application =
                  Provider.of<BuilderModel>(context, listen: false).app;
              var env = getDefaultEnv(application);
              env.addEntries(
                Map<String, dynamic>.from(dataEnv).entries,
              );
              model.updateEnv(env);
            },
            label: Text(title ?? 'Click to update env'),
            icon: const Icon(
              Icons.download_done_rounded,
              size: 16,
            ),
          );
        }
      case ActionType.exportConfig:
        return Container(
          constraints: BoxConstraints(
            maxWidth: MediaQuery.of(context).size.width * 0.3,
          ),
          child: ChangeNotifierProvider(
            create: (context) => ExportConfigViewmodel(),
            child: const SizedBox(
              height: 500,
              child: ExportConfigPage(),
            ),
          ),
        );
      case ActionType.retry:
        final title = action?['title'];

        return OutlinedButton.icon(
          style: OutlinedButton.styleFrom(
            backgroundColor:
                Theme.of(context).colorScheme.surfaceContainerHighest,
          ),
          onPressed: () {
            context.read<ChatStateManager>().reSendMessage();
          },
          label: Text(title ?? 'Retry'),
          icon: const Icon(
            Icons.refresh,
            size: 16,
          ),
        );
      default:
    }
    return const SizedBox();
  }
}
