import logging
import traceback
from logging.handlers import RotatingFileHandler
from pathlib import Path

from langchain_core.callbacks.base import BaseCallbackHandler

# Configure logging
Path("logs/").mkdir(exist_ok=True)
logging.basicConfig(
    level=logging.DEBUG,
    format="%(asctime)s, %(name)s, %(levelname)s, %(funcName)s, %(message)s",
    handlers=[
        RotatingFileHandler("logs/logs.txt", maxBytes=100000, backupCount=10),
    ],
)

logger = logging.getLogger("GenAI")


def log_error(error: Exception):
    """Log error details with stack trace"""

    print(error)
    print(traceback.format_exc())

    logger.error("Error: %s", str(error))
    logger.error("Traceback:\n%s", "".join(traceback.format_tb(error.__traceback__)))


class PromptDebugHandler(BaseCallbackHandler):
    def on_llm_start(self, serialized, prompts, **kwargs):
        # Print to console
        print("\n" + "=" * 50)
        print("PROMPT DEBUG OUTPUT")
        print("=" * 50)

        # Log to file
        logger.info("\n" + "=" * 50)
        logger.info("PROMPT DEBUG OUTPUT")
        logger.info("=" * 50)

        # Print each message in the prompt
        if isinstance(prompts[0], str):
            print("\nRaw Prompt:")
            print(prompts[0])
            logger.info("\nRaw Prompt:")
            logger.info(prompts[0])
        else:
            print("\nPrompt Messages:")
            logger.info("\nPrompt Messages:")
            for msg in prompts[0].messages:
                print(f"\n[{msg.type.upper()}]:")
                print(msg.content)
                logger.info(f"\n[{msg.type.upper()}]:")
                logger.info(msg.content)

        print("\n" + "=" * 50)
        logger.info("\n" + "=" * 50)

    def on_llm_end(self, response, **kwargs):
        # Print to console
        print("\n" + "=" * 50)
        print("LLM RESPONSE")
        print("=" * 50)

        # Log to file
        logger.info("\n" + "=" * 50)
        logger.info("LLM RESPONSE")
        logger.info("=" * 50)

        if hasattr(response, "generations"):
            for generation in response.generations:
                for g in generation:
                    print("\nResponse:")
                    print(g.text)
                    logger.info("\nResponse:")
                    logger.info(g.text)

        print("\n" + "=" * 50)
        logger.info("\n" + "=" * 50)
