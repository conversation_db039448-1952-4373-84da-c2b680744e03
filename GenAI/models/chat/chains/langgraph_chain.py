from typing import Any, Dict, List, Literal, TypedDict, Union

from langchain.chains.history_aware_retriever import create_history_aware_retriever
from langchain_core.messages import SystemMessage, convert_to_messages
from langchain_core.prompts import (
    ChatPromptTemplate,
    HumanMessagePromptTemplate,
    MessagesPlaceholder,
    PromptTemplate,
    SystemMessagePromptTemplate,
)
from langchain_core.runnables import Runnable
from langgraph.graph import END, START, Graph, StateGraph
from pydantic import BaseModel, Field
from utils.logger import PromptDebugHandler, log_error, logger

from ...app_models import Action, ChatRequest, ChatResponse
from ...model_factory import ModelFactory
from ...token_manager import TokenManager
from ..models.models import ValidityModel
from ..stores.knowledge_store import KnowledgeStore
from .chat_constants import *
from .env_tool import EnvTool


class RouteQuery(BaseModel):
    """Route the lastest user message to the most relevant query type."""

    route: Literal["information_query", "update_feature_query"] = Field(
        description="Given a user messages, choose to route it to the most relevant query type.",
    )


class AgentState(TypedDict):
    messages: List[Dict]
    app_details: Dict
    user_details: Dict
    env_dart: Dict
    documents: List[str]
    response: Union[ChatResponse, None]
    router_result: Union[RouteQuery, None]
    license_result: Union[ValidityModel, None]
    optimized_env: Union[Dict, None]


class LangGraphChain:
    def __init__(self, knowledge_store: KnowledgeStore, model_provider: str):
        # Initialize based LLM
        based_llm = ModelFactory.create_model(provider=model_provider)
        self.llm = based_llm.with_config({"callbacks": [PromptDebugHandler()]})

        # Initialize retriever
        self.retriever = knowledge_store.vectorstore.as_retriever(
            search_type="similarity",
            search_kwargs={"k": 2, "fetch_k": 5},
        )
        self.env_tool = EnvTool(llm=self.llm)
        self.graph = self._create_graph()

    def _create_router_node(self) -> Runnable:
        """Create a router node to determine the type of query."""

        router_template = """
<instructions>
You are an expert at routing queries to the most relevant query type. The
update_feature_query is only for updating feature in the app. Otherwise, it is
information_query.
</instructions>
"""

        router_prompt = ChatPromptTemplate.from_messages(
            [
                SystemMessagePromptTemplate.from_template(router_template),
                MessagesPlaceholder(variable_name="messages"),
            ]
        )

        structured_llm = self.llm.with_structured_output(RouteQuery)

        async def route_query(state: AgentState, config) -> Dict[str, Any]:
            print("\n🔀 ROUTER NODE - Starting query routing...")
            logger.info("\n🔀 ROUTER NODE - Starting query routing...")

            result = await (router_prompt | structured_llm).ainvoke(
                {
                    "messages": state["messages"],
                },
                config,
            )

            print(f"🔀 ROUTER RESULT: {result}")
            logger.info(f"🔀 ROUTER RESULT: {result}")
            return {"router_result": result}

        return route_query

    def _create_license_check_node(self) -> Runnable:
        """Create a license check node to validate feature access."""

        license_template = """
<instructions>
Analyze the user messages, the `environment_details`, `documentation_context`
and `plan_license_rule` sections to check if the user has permissions.
</instructions>

<plan_license_rule>
{plan_license_rule}
</plan_license_rule>

<documentation_context>
{documents}
</documentation_context>

<environment_details>
# App Details:
{app_details}

# User Details (the user is logged in if provided):
{user_details}
</environment_details>

<prompt_security>
{prompt_security}
</prompt_security>
"""

        license_prompt = ChatPromptTemplate.from_messages(
            [
                SystemMessagePromptTemplate(
                    prompt=PromptTemplate(
                        input_variables=["documents", "app_details", "user_details"],
                        template=license_template,
                        partial_variables={
                            "system_prompt": CHAT_SYSTEM_PROMPT,
                            "plan_license_rule": CHAT_FLUXBUILDER_PLAN_DETAILS,
                            "prompt_security": CHAT_SECURITY_REMINDER,
                        },
                    )
                ),
                MessagesPlaceholder(variable_name="messages"),
            ]
        )

        structured_llm = self.llm.with_structured_output(ValidityModel)

        async def check_license(state: AgentState, config) -> AgentState:
            try:
                print("\n🔐 LICENSE CHECK NODE - Checking permissions...")
                logger.info("\n🔐 LICENSE CHECK NODE - Checking permissions...")

                result = await (license_prompt | structured_llm).ainvoke(
                    {
                        "messages": state["messages"],
                        "documents": state["documents"],
                        "app_details": state["app_details"],
                        "user_details": state["user_details"],
                    },
                    config,
                )

                print(f"🔐 LICENSE CHECK RESULT: {result}")
                logger.info(f"🔐 LICENSE CHECK RESULT: {result}")
                return {"license_result": result}
            except Exception as e:
                print(f"🔐 LICENSE CHECK ERROR: {e}")
                logger.error(f"🔐 LICENSE CHECK ERROR: {e}")
                return {
                    "license_result": ValidityModel(
                        is_valid=False,
                        response="We cannot process your request due to limitations or permissions. Please try contacting support.",
                    ),
                }

        return check_license

    def _create_env_optimizer_node(self) -> Runnable:
        """Create an environment optimizer node to reduce token usage."""

        async def optimize_env(state: AgentState) -> AgentState:
            messages = state["messages"]
            env_dart = state["env_dart"]
            documents = state["documents"]

            optimized_env = await self.env_tool._arun(messages, env_dart, documents)
            return {"optimized_env": optimized_env}

        return optimize_env

    def _create_document_retrieval_node(self) -> Runnable:
        """Create a document retrieval node to fetch relevant documents."""

        documents_template = """
Analyze the user messages and current question to create a search query that
will find the most relevant FluxBuilder documentation. Focus on technical
details, features information.
"""

        documents_prompt = ChatPromptTemplate.from_messages(
            [
                SystemMessagePromptTemplate.from_template(documents_template),
                MessagesPlaceholder(variable_name="chat_history"),
                HumanMessagePromptTemplate.from_template("{input}"),
            ]
        )

        history_aware_retriever = create_history_aware_retriever(
            self.llm, self.retriever, documents_prompt
        )

        async def retrieve_documents(state: AgentState, config) -> AgentState:
            try:
                print("\n📚 DOCUMENT RETRIEVAL NODE - Searching for relevant docs...")
                logger.info(
                    "\n📚 DOCUMENT RETRIEVAL NODE - Searching for relevant docs..."
                )

                docs = await history_aware_retriever.ainvoke(
                    {
                        "input": state["messages"][-1].content,
                        "chat_history": state["messages"],
                    },
                    config,
                )
                # Convert documents to string with page_content and metadata
                formatted_docs = [
                    (f"Source: {doc.metadata}\n" f"Content: {doc.page_content}")
                    for doc in docs
                ]

                print(f"📚 RETRIEVED {len(formatted_docs)} DOCUMENTS")
                logger.info(f"📚 RETRIEVED {len(formatted_docs)} DOCUMENTS")
                return {"documents": formatted_docs}
            except Exception as e:
                print(f"📚 DOCUMENT RETRIEVAL ERROR: {e}")
                log_error(e)
                return {}

        return retrieve_documents

    def _create_info_response_node(self) -> Runnable:
        """Create an information response node for handling information queries."""
        prompt_template = """
<instructions>
{system_prompt}
</instructions>

<response_rule>
{response_rule}
</response_rule>

<plan_license_rule>
{plan_license_rule}
</plan_license_rule>

<documentation_context>
{documents}
</documentation_context>

<environment_details>
# App Details:
{app_details}

# User Details (the user is logged in if provided):
{user_details}
</environment_details>

<prompt_security>
{prompt_security}
</prompt_security>
"""

        prompt = ChatPromptTemplate.from_messages(
            [
                SystemMessagePromptTemplate(
                    prompt=PromptTemplate(
                        template=prompt_template,
                        input_variables=["documents", "app_details", "user_details"],
                        partial_variables={
                            "system_prompt": CHAT_SYSTEM_PROMPT,
                            "plan_license_rule": CHAT_FLUXBUILDER_PLAN_DETAILS,
                            "prompt_security": CHAT_SECURITY_REMINDER,
                            "response_rule": RESPONSE_RULE,
                        },
                    )
                ),
                MessagesPlaceholder(variable_name="messages"),
            ]
        )

        structured_llm = self.llm.with_structured_output(ChatResponse)

        async def get_response(state: AgentState, config) -> AgentState:
            try:
                print("\n💬 INFO RESPONSE NODE - Generating information response...")
                logger.info(
                    "\n💬 INFO RESPONSE NODE - Generating information response..."
                )

                result = await (prompt | structured_llm).ainvoke(
                    {
                        "messages": state["messages"],
                        "documents": state["documents"],
                        "app_details": state["app_details"],
                        "user_details": state["user_details"],
                    },
                    config,
                )

                print(f"💬 INFO RESPONSE GENERATED: {result.response[:100]}...")
                logger.info(f"💬 INFO RESPONSE GENERATED: {result.response[:100]}...")
                return {"response": result}
            except Exception as e:
                print(f"💬 INFO RESPONSE ERROR: {e}")
                log_error(e)
                return {
                    "response": ChatResponse(
                        response="An error occurred while processing your request. Please try again later.",
                        action=[Action(type="retry", title="Retry")],
                        suggestQueries=[],
                    ),
                }

        return get_response

    def _create_feature_update_node(self) -> Runnable:
        """Create a feature update node for handling feature modifications."""
        prompt_template = """
<instructions>
{system_prompt}
</instructions>

<response_rule>
{response_rule}
</response_rule>

<documentation_context>
{documents}
</documentation_context>

<environment_details>
# App Details:
{app_details}

# App Settings (env.dart):
{env_dart}
</environment_details>

<prompt_security>
{prompt_security}
</prompt_security>
"""

        prompt = ChatPromptTemplate.from_messages(
            [
                SystemMessagePromptTemplate(
                    prompt=PromptTemplate(
                        template=prompt_template,
                        input_variables=["documents", "app_details", "env_dart"],
                        partial_variables={
                            "system_prompt": CHAT_SYSTEM_PROMPT,
                            "prompt_security": CHAT_SECURITY_REMINDER,
                            "response_rule": RESPONSE_RULE,
                        },
                    )
                ),
                MessagesPlaceholder(variable_name="messages"),
            ]
        )

        structured_llm = self.llm.with_structured_output(ChatResponse)

        async def format_response(state: AgentState, config) -> AgentState:
            try:
                print(
                    "\n🔧 FEATURE UPDATE NODE - Generating feature update response..."
                )
                logger.info(
                    "\n🔧 FEATURE UPDATE NODE - Generating feature update response..."
                )

                result = await (prompt | structured_llm).ainvoke(
                    {
                        "messages": state["messages"],
                        "documents": state["documents"],
                        "app_details": state["app_details"],
                        "env_dart": state["optimized_env"] or state["env_dart"],
                    },
                    config,
                )

                print(
                    f"🔧 FEATURE UPDATE RESPONSE GENERATED: {result.response[:100]}..."
                )
                logger.info(
                    f"🔧 FEATURE UPDATE RESPONSE GENERATED: {result.response[:100]}..."
                )
                return {"response": result}
            except Exception as e:
                print(f"🔧 FEATURE UPDATE ERROR: {e}")
                log_error(e)
                return {
                    "response": ChatResponse(
                        response="An error occurred while processing your request. Please try again later.",
                        action=[Action(type="retry", title="Retry")],
                        suggestQueries=[],
                    ),
                }

        return format_response

    def _merge_inputs_node(self) -> Runnable:
        """Initialize the agent state by merging data from document retrieval and router."""

        async def merge_inputs(state: AgentState) -> AgentState:
            return state

        return merge_inputs

    def _create_graph(self) -> Graph:
        """Create the LangGraph flow."""
        workflow = StateGraph(AgentState)

        # Add nodes
        workflow.add_node("document_retrieval", self._create_document_retrieval_node())
        workflow.add_node("router", self._create_router_node())
        workflow.add_node("merge_inputs", self._merge_inputs_node())
        workflow.add_node("license_check", self._create_license_check_node())
        workflow.add_node("env_optimizer", self._create_env_optimizer_node())
        workflow.add_node("info_response", self._create_info_response_node())
        workflow.add_node("feature_update", self._create_feature_update_node())

        # Set up parallel execution
        workflow.add_edge(START, "document_retrieval")
        workflow.add_edge(START, "router")
        workflow.add_edge("document_retrieval", "merge_inputs")
        workflow.add_edge("router", "merge_inputs")

        # Define the condition for router
        def route_condition(state: AgentState) -> str:
            if not state.get("router_result"):
                return "information_query"
            # Check correct route
            if ["information_query", "update_feature_query"].count(
                state["router_result"].route
            ) == 0:
                return "information_query"
            return state["router_result"].route

        # Add conditional edges from merge_inputs
        workflow.add_conditional_edges(
            "merge_inputs",
            route_condition,
            {
                "update_feature_query": "license_check",
                "information_query": "info_response",
            },
        )

        # Define the condition for license check
        def should_proceed(state: AgentState) -> bool:
            if not state.get("license_result"):
                return False
            return state["license_result"].is_valid

        workflow.add_conditional_edges(
            "license_check", should_proceed, {True: "env_optimizer", False: END}
        )
        workflow.add_edge("env_optimizer", "feature_update")
        workflow.add_edge("feature_update", END)
        workflow.add_edge("info_response", END)

        return workflow.compile()

    def visualize_graph(self):
        """Visualize the LangGraph flow using Mermaid."""

        print(self.graph.get_graph().draw_mermaid())

    @TokenManager.track_tokens
    async def process_messages(self, request: ChatRequest) -> ChatResponse:
        """Process messages using the LangGraph flow."""
        try:
            print("\n🚀 LANGGRAPH CHAIN - Starting message processing...")
            logger.info("\n🚀 LANGGRAPH CHAIN - Starting message processing...")

            messages = convert_to_messages(request.messages[-5:])

            # Initialize empty dictionaries for missing context
            environment_details = request.context or {}

            # Safely access nested attributes with defaults
            app_details = {}
            user_details = {}
            env_dart = {}

            if (
                hasattr(environment_details, "app_details")
                and environment_details.app_details
            ):
                app_details = environment_details.app_details.model_dump()

            if (
                hasattr(environment_details, "user_details")
                and environment_details.user_details
            ):
                user_details = environment_details.user_details.model_dump()

            if hasattr(environment_details, "env_dart"):
                env_dart = environment_details.env_dart or {}

            # Initialize state with default values
            initial_state = {
                "messages": messages,
                "app_details": app_details,
                "user_details": user_details,
                "env_dart": env_dart,
                "documents": [],
                "response": None,
                "router_result": None,
                "license_result": None,
                "optimized_env": None,
            }

            print(
                f"🚀 INITIAL STATE: {len(messages)} messages, app_details: {bool(app_details)}, user_details: {bool(user_details)}"
            )
            logger.info(
                f"🚀 INITIAL STATE: {len(messages)} messages, app_details: {bool(app_details)}, user_details: {bool(user_details)}"
            )

            # Run the graph
            print("\n🚀 EXECUTING LANGGRAPH WORKFLOW...")
            logger.info("\n🚀 EXECUTING LANGGRAPH WORKFLOW...")
            final_state = await self.graph.ainvoke(initial_state)

            # Extract response from the final state
            if final_state.get("response"):
                return final_state["response"]
            elif (
                final_state.get("license_result")
                and final_state["license_result"].is_valid is False
            ):
                # Return error response for invalid license
                if final_state["license_result"].response:
                    return ChatResponse(response=final_state["license_result"].response)
                return ChatResponse(
                    response="We cannot process your request due to limitations or permissions. Please try contacting support."
                )
            else:
                # Return a default error response
                return ChatResponse(
                    response="An error occurred while processing your request. Please try again later.",
                    action=[Action(type="retry", title="Retry")],
                    suggestQueries=[],
                )

        except Exception as e:
            log_error(e)
            return ChatResponse(
                response="An error occurred while processing your request. Please try again later.",
                action=[Action(type="retry", title="Retry")],
                suggestQueries=[],
            )
