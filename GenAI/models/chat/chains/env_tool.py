from typing import Any, Dict, List, Optional

from langchain_core.documents import Document
from langchain_core.messages import AIMessage
from langchain_core.prompts import (
    ChatPromptTemplate,
    MessagesPlaceholder,
    SystemMessagePromptTemplate,
)
from langchain_core.runnables import Runnable


class EnvTool:
    """Tool for extracting and optimizing environment parameters for specific features."""

    def __init__(self, llm: Optional[Runnable] = None):
        """Initialize the environment tool with optional LLM for feature key extraction."""
        self.llm = llm

    def _extract_feature_keys(
        self, messages: List[Dict], documents: str = None
    ) -> List[str]:
        """Extract relevant feature keys from the user messages using document context."""
        if not self.llm:
            return []

        if not documents:
            return []

        prompt_template = """
<instructions>
You are an expert at extracting environmental parameter keys from user queries.
Use the provided document context to better understand which keys are relevant.
</instructions>

<response_rule>
Extract only the parent feature keys from `All Related Configs` that need
updates from the user messages. For example:
All Related Configs:  
```json
{{
    "advanceConfig": {{
        "EnableFirebase": false,
    }}
}}
```
and
All Related Configs:  
```json
{{
    "enableRemoteConfigFirebase": false,
}}
```
Return: advanceConfig,enableRemoteConfigFirebase
</response_rule>

<response_format>
Return only the keys as a comma-separated list.
</response_format>

<documentation_context>
{doc_context}
</documentation_context>
"""

        prompt = ChatPromptTemplate.from_messages(
            [
                SystemMessagePromptTemplate.from_template(prompt_template),
                MessagesPlaceholder(variable_name="messages"),
            ]
        )

        response = self.llm.invoke(
            prompt.format_messages(
                messages=messages,
                doc_context=documents,
            )
        )

        if isinstance(response, AIMessage):
            content = response.content
        else:
            content = str(response)

        return [key.strip() for key in content.split(",")]

    def _run(
        self, messages: List[Dict], env_dart: Dict[str, Any], documents: str = None
    ) -> Dict[str, Any]:
        """
        Extract and optimize environment parameters based on the user messages and documents.

        Args:
            messages: User messages to analyze for feature keys
            env_dart: Complete environment dictionary from user input
            documents: List of retrieved documents to use as context

        Returns:
            Dict containing:
            - Full env_dart if feature_keys is empty
            - Empty dict if both env_dart and feature_keys are empty
            - Only necessary parameters if both env_dart and feature_keys are available
        """
        # Extract feature keys from messages using document context
        feature_keys = self._extract_feature_keys(messages, documents)

        # If no env_dart provided, return empty dict
        if not env_dart:
            return {}

        # If no feature keys found, return full env_dart
        if not feature_keys:
            return env_dart

        # Extract only relevant parameters
        extracted_params = {}
        for key in feature_keys:
            if key in env_dart:
                extracted_params[key] = env_dart[key]

        # If no relevant parameters found, return full env_dart
        if not extracted_params:
            return env_dart

        return extracted_params

    async def _arun(
        self, messages: List[Dict], env_dart: Dict[str, Any], documents: str = None
    ) -> Dict[str, Any]:
        """Async implementation of the tool."""
        return self._run(messages, env_dart, documents)
