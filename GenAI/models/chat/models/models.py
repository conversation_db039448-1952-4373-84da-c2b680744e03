from typing import Optional

from pydantic import BaseModel, Field


class ValidityModel(BaseModel):
    is_valid: bool = Field(
        description="Check if the user has permission to perform the query based on the plan/license/subscription"
    )
    """Check if the user has permission to perform the query based on the plan/license/subscription"""
    response: Optional[str] = Field(
        default=None,
        description="A message that explains why the query is not allowed.",
    )
    """A message that explains why the query is not allowed."""
