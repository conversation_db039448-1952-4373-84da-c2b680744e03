from config.settings import *
from langchain.callbacks.streaming_stdout import StreamingStdOutCallbackHandler
from langchain.chat_models.base import BaseChatModel
from langchain_anthropic import ChatAnthropic
from langchain_core.embeddings import Embeddings
from langchain_google_genai import Chat<PERSON><PERSON>gleGenerativeA<PERSON>, GoogleGenerativeAIEmbeddings
from langchain_huggingface import HuggingFaceEmbeddings
from langchain_ollama import Chat<PERSON>lla<PERSON>
from langchain_openai import AzureChatOpenAI, ChatOpenAI, OpenAIEmbeddings


class ModelFactory:
    @staticmethod
    def create_model(
        provider: str = ModelConfig.DEFAULT_PROVIDER,
        streaming: bool = False,
        **kwargs,
    ) -> BaseChatModel:
        """Create a chat model instance.

        Args:
            provider: The model provider ("anthropic" or "openai")
            streaming: Whether to enable streaming
            **kwargs: Additional model parameters
        """
        callbacks = [StreamingStdOutCallbackHandler()] if streaming else None

        if provider == "anthropic":
            return ChatAnthropic(
                anthropic_api_key=ANTHROPIC_API_KEY,
                model=ModelConfig.CLAUDE_MODEL,
                temperature=kwargs.get("temperature", ModelConfig.TEMPERATURE),
                streaming=streaming,
                callbacks=callbacks,
            )
        elif provider == "openai" and OPENAI_API_KEY:
            return ChatOpenAI(
                openai_api_key=OPENAI_API_KEY,
                model_name=ModelConfig.OPENAI_MODEL,
                temperature=kwargs.get("temperature", ModelConfig.TEMPERATURE),
                streaming=streaming,
                callbacks=callbacks,
            )
        elif provider == "openrouter" and OPENROUTER_API_KEY:

            return ChatOpenAI(
                openai_api_key=OPENROUTER_API_KEY,
                openai_api_base="https://openrouter.ai/api/v1",
                model_name=ModelConfig.OPENROUTER_MODEL,
                default_headers={
                    "HTTP-Referer": "https://fluxbuilder.com/",
                    "X-Title": "FluxBuilder",
                },
                temperature=kwargs.get("temperature", ModelConfig.TEMPERATURE),
                streaming=streaming,
                callbacks=callbacks,
            )
        elif provider == "gemini" and GEMINI_API_KEY:
            return ChatGoogleGenerativeAI(
                google_api_key=GEMINI_API_KEY,
                model=ModelConfig.GEMINI_MODEL,
                temperature=kwargs.get("temperature", ModelConfig.TEMPERATURE),
                callbacks=callbacks,
            )
        elif provider == "openai":
            raise ValueError(
                "OpenAI API key not found. Please set OPENAI_API_KEY in environment variables."
            )
        elif provider == "azureOpenai" and AZURE_OPENAI_API_KEY:
            return AzureChatOpenAI(
                api_key=AZURE_OPENAI_API_KEY,
                azure_endpoint=AZURE_OPENAI_ENDPOINT,
                azure_deployment=ModelConfig.AZURE_OPENAI_MODEL,
                api_version=ModelConfig.AZURE_OPENAI_API_VERSION,
                temperature=kwargs.get("temperature", ModelConfig.TEMPERATURE),
                streaming=streaming,
                callbacks=callbacks,
            )
        elif provider == "azureOpenai":
            raise ValueError(
                "Azure OpenAI API key not found. Please set AZURE_OPENAI_API_KEY in environment variables."
            )
        elif provider == "ollama":
            return ChatOllama(
                base_url=ModelConfig.OLLAMA_BASE_URL,
                model=ModelConfig.OLLAMA_MODEL,
                temperature=kwargs.get("temperature", ModelConfig.TEMPERATURE),
            )
        else:
            raise ValueError(f"Unsupported model provider: {provider}")

    @staticmethod
    def create_embedding_model(
        provider: str = ModelConfig.DEFAULT_PROVIDER,
    ) -> Embeddings:
        """Create a chat embedding model instance."""

        if provider == "gemini" and GEMINI_API_KEY:
            return GoogleGenerativeAIEmbeddings(
                model=ModelConfig.GEMINI_EMBEDDING_MODEL, google_api_key=GEMINI_API_KEY
            )
        elif GEMINI_API_KEY:
            return GoogleGenerativeAIEmbeddings(
                model=ModelConfig.GEMINI_EMBEDDING_MODEL, google_api_key=GEMINI_API_KEY
            )
        elif provider == "huggingface":
            return HuggingFaceEmbeddings(
                model_name="all-MiniLM-L6-v2",  # Small, fast model good for most use cases
                model_kwargs={"device": "cpu"},
            )
        elif provider == "openai" and OPENAI_API_KEY:
            return OpenAIEmbeddings(
                openai_api_key=OPENAI_API_KEY,
                model=ModelConfig.OPENAI_EMBEDDING_MODEL,
            )
        elif provider == "openai":
            raise ValueError(
                "OpenAI API key not found. Please set OPENAI_API_KEY in environment variables."
            )
        else:
            raise ValueError(f"Unsupported model provider: {provider}")
