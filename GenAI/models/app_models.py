from typing import Dict, List, Optional, Union

from config.settings import ModelConfig
from pydantic import BaseModel, Field, field_validator
from typing_extensions import TypedDict


class Message(TypedDict):
    role: str = Field(description="User or Assistant")
    """User or Assistant"""
    content: Optional[str] = Field(default=None, description="Full message content")
    """Full message content"""

    def __getitem__(self, item):
        return getattr(self, item)

class AppDetails(BaseModel):
    app_id: Optional[str] = Field(default=None, description="The user application ID, also used to manage chat thread")
    """The user application ID, also used to manage chat thread"""
    app_name: Optional[str] = Field(default=None, description="The name of the user application")
    """The name of the user application"""
    app_category: Optional[str] = Field(default=None, description="The category of the user application")
    """The category of the user application"""
    description: Optional[str] = Field(default=None, description="The description of the user application")
    """The description of the user application"""
    framework: Optional[str] = Field(default=None, description="The platform/framework of the user application")
    """The platform/framework of the user application"""
    app_plan: Optional[str] = Field(default=None, description="The plan/license of the user application")
    """The plan/license of the user application"""
    app_type: Optional[str] = Field(default=None, description="The type of the user application")
    """The type of the user application"""


class UserDetails(BaseModel):
    user_email: Optional[str] = Field(default=None, description="The email of the user")
    """The email of the user"""
    user_name: Optional[str] = Field(default=None, description="The name of the user")
    """The name of the user"""
    user_subscription: Optional[str] = Field(default=None, description="The subscription of the user")
    """The subscription of the user"""


class BuilderContextModel(BaseModel):
    app_details: Optional[AppDetails] = Field(default=None, description="The details of the user application")
    """The details of the user application"""
    user_details: Optional[UserDetails] = Field(default=None, description="The details of the user")
    """The details of the user"""
    env_dart: Optional[Dict] = Field(default=None, description="The details of the user app settings")
    """The details of the user app settings"""

    def __str__(self):
        return f"""App Details:
{self.app_details}

User Details (the user is logged in if provided):
{self.user_details}

App Settings (env.dart)
{self.env_dart}
"""

class ChatRequest(BaseModel):
    messages: List[Message] = Field(description="List of messages in the conversation")
    """List of messages in the conversation"""
    context: Optional[BuilderContextModel] = Field(default=None, description="Optional FluxBuilder context information about the app and user")
    """Optional FluxBuilder context information about the app and user"""
    stream: bool = Field(default=False, description="Whether to stream the response")
    """Whether to stream the response"""

    @field_validator("messages")
    def validate_messages(cls, v: List[Message]):
        """Validates that messages array is not empty and contains valid content"""
        if not v:
            raise ValueError("Messages array cannot be empty")
        for msg in v:
            if not msg["content"]:
                raise ValueError("Message must have content")
        return v


class Action(BaseModel):
    type: str = Field(
        description="""The type of the action. Available action type:
    - `updateEnv`: To update env.dart. Updated env_dart json data required
    - `navigate`: To navigate to a screen. FluxBuilder internal route required
    - `openUrl`: To open a url. Url required
    """
    )
    """The type of the action. Available action type:
    - `updateEnv`: To update env.dart. Updated env_dart json data required
    - `navigate`: To navigate to a screen. FluxBuilder internal route required
    - `openUrl`: To open a url. Url required
    """
    title: str = Field(
        description="The title of the action with the user's language. For example: 'Apply Changes', 'Open Firebase Analytics', 'Open FluxBuilder Documentation'"
    )
    """The title of the action with the user's language. For example: 'Apply Changes', 'Open Firebase Analytics', 'Open FluxBuilder Documentation'"""
    data: Optional[Union[Dict, str]] = Field(
        default=None,
        description="The data of the action. For example: {'enableFirebaseAnalytics': true}, 'featureFirebaseAnalytics', 'https://docs.fluxbuilder.com'"
    )
    """The data of the action. For example: {'enableFirebaseAnalytics': true}, 'featureFirebaseAnalytics', 'https://docs.fluxbuilder.com'"""


class ChatResponse(BaseModel):
    response: str = Field(
        description="Conversational responses to user queries, in the user's language, markdown format, human-readable"
    )
    """Conversational responses to user queries, in the user's language, markdown format, human-readable"""
    action: Optional[List[Action]] = Field(
        default=None,
        description="The next optional actions based on the user's query, are used to decide the next actions for user app on the FluxBuilder. The actions will be displayed next to the response message on FluxBuilder",
    )
    """The next optional actions based on the user's query, are used to decide the next actions for user app on the FluxBuilder. The actions will be displayed next to the response message on FluxBuilder"""
    suggestQueries: Optional[List[str]] = Field(
        default=None,
        description="A list of suggested next queries for the user based on the user's previous queries",
    )
    """A list of suggested next queries for the user based on the user's previous queries"""


class ChatIntegrationRequest(ChatRequest):
    tone: str = Field(default='CASUAL_FRIENDLY_TONE', description="The AI tone")

class ChatTemplateRequest(ChatRequest):
    tone: str = Field(default='CASUAL_FRIENDLY_TONE', description="The AI tone")
    mainColor: Optional[str] = Field(default=None, description="The Main Color in hex format")
    logo: Optional[str] = Field(default=None, description="The logo URL")
    purposes: Optional[List[str]] = Field(default=None, description="List purposes gereated")
    categories: Optional[List[str]] = Field(default=None, description="List categories generated")
    appCategory: Optional[str] = Field(default=None, description="The app category key")
    purpose: Optional[str] = Field(default=None, description="The app purpose")

class ChatBeautifulSoupRequest(BaseModel):
    question: str = Field(description="The question")
    is_interrupt: bool = Field(default=False, description="Whether to interrupt the conversation")
    id_session: str = Field(default=None, description="The session ID")
    tone: str = Field(default='CASUAL_FRIENDLY_TONE', description="The AI tone")

class ChatBeautifulSoupTestRequest(ChatRequest):
    type: str = Field(description="The type")
    tone: str = Field(default='CASUAL_FRIENDLY_TONE', description="The AI tone")
