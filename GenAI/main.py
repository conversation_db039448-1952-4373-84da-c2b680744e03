import uvicorn
from config.settings import ModelConfig
from models.api_service import app
from models.chat.chains.langgraph_chain import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from models.chat.stores.knowledge_store import KnowledgeStore
from models.wizard.integration_chain import <PERSON><PERSON>hain
from models.wizard.template_chain import Template<PERSON><PERSON><PERSON>
from models.wizard.beautifulsoup.beautifulsoup_chain import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>


def init_app():
    if not hasattr(app.state, "initialized"):
        # Initialize knowledge store with minimal settings
        knowledge_store = KnowledgeStore(
            model_provider="huggingface"  # Using lightweight huggingface model
        )
        model_provider = ModelConfig.DEFAULT_PROVIDER

        # Initialize chains
        app.state.chat_chain = LangGraphChain(
            knowledge_store=knowledge_store, model_provider=model_provider
        )
        app.state.chat_chain.visualize_graph()
        app.state.integration_chain = IntegrationChain(model_provider=model_provider)
        app.state.template_chain = Template<PERSON>hain(model_provider=model_provider)
        app.state.beautifulsoup_chain = BeautifulSoupChain(model_provider=model_provider)
        app.state.initialized = True

    return app


def create_app():
    return init_app()


if __name__ == "__main__":
    uvicorn.run(
        "main:create_app",
        host="127.0.0.1",
        port=50001,
        reload=True,
        factory=True,
    )

# Add this line at the end of the file
app = create_app()
