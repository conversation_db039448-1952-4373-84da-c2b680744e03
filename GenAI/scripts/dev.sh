#!/bin/bash
set -e

# Set Python command based on availability
if command -v python3.11 &> /dev/null; then
    PYTHON_CMD=python3.11
elif command -v python3.10 &> /dev/null; then
    PYTHON_CMD=python3.10
else
    PYTHON_CMD=python3
fi

echo "Using Python command: $PYTHON_CMD"

# Create logs directory if it doesn't exist
mkdir -p logs

# Create virtual environment if it doesn't exist
if [ ! -d "venv" ]; then
    echo "Creating virtual environment..."
    $PYTHON_CMD -m venv venv || {
        echo "Failed to create virtual environment"
        exit 1
    }
fi

# Ensure we're using the virtual environment's Python and pip
VENV_PYTHON="venv/bin/python3"
VENV_PIP="venv/bin/pip"

# Activate virtual environment
source venv/bin/activate || {
    echo "Failed to activate virtual environment"
    exit 1
}

# Upgrade pip in virtual environment
upgrade_pip() {
    echo "Upgrading pip..."
    $VENV_PYTHON -m pip install --upgrade pip
}

# Function to install packages with error handling
install_packages() {
    local group_name=$1
    shift
    echo "Installing $group_name..."
    for package in "$@"; do
        echo "Installing $package..."
        $VENV_PIP install --no-cache-dir "$package" || {
            echo "Failed to install $package"
            return 1
        }
    done
}

# Install dependencies in groups
install() {
    echo "Installing dependencies..."

    # Base dependencies
    install_packages "Base Dependencies" \
        "fastapi==0.104.0" \
        "uvicorn==0.24.0" \
        "pydantic>=2.4.2"

    # Embeddings and Vector Search
    install_packages "Embeddings" \
        "faiss-cpu>=1.7.4"

    # LangChain and AI
    install_packages "LangChain" \
        "langchain>=0.1.0" \
        "langchain-core>=0.1.10" \
        "langchain-community>=0.0.13" \
        "langchain-openai>=0.0.5" \
        "langchain-google-genai>=2.0.8" \
        "langchain-anthropic>=0.0.9" \
        "langchain-huggingface>=0.0.3" \
        "langchain-ollama>=0.2.3" \
        "langgraph>=0.4.6"

    # Auth and utils
    install_packages "Auth and Utils" \
        "PyJWT>=2.8.0" \
        "python-dotenv>=1.0.0"

    # Exporting
    install_packages "Exporting" \
        "pipreqs>=0.4.13"
}

# Function to verify package installation
check_package() {
    $VENV_PYTHON -c "import $1" 2>/dev/null
    return $?
}

# Verify critical packages
check() {
    echo "Verifying installations..."
    for package in "fastapi" "uvicorn" "langchain"; do
        echo "Checking $package..."
        if ! check_package $package; then
            echo "Error: $package is not properly installed"
            exit 1
        fi
    done
}

# Export packages
export_packages() {
    echo "Exporting packages..."
    $VENV_PYTHON -m pipreqs.pipreqs . --force
    
    # Check if faiss-cpu is in requirements.txt, if not add it
    if ! grep -q "faiss-cpu" requirements.txt; then
        echo "Adding faiss-cpu to requirements.txt..."
        echo "faiss-cpu>=1.7.4" >> requirements.txt
    fi
}

case "$1" in
    "install")
        upgrade_pip
        install
        check
        echo "✅ Dependencies installed successfully"
        ;;
    "run")
        check
        $VENV_PYTHON main.py
        ;;
    "export")
        export_packages
        ;;
    *)
        echo "Usage: ./dev.sh [install|run]"
        echo "  install: Only install dependencies"
        echo "  run: Run server"
        echo "  export: Export packages"
        ;;
esac 