# Firebase Features

## Firebase Settings

Description: Configure Firebase core settings.

Title: Firebase

Route: `featureFirebase`

Document URL: https://docs.fluxbuilder.com/firebase-settings

Include these settings for app: 

- Enable Firebase

All Related Configs:

```json
{
    "advanceConfig": {
        "EnableFirebase": false,
    }
}
```

## Firebase Push Notification

Description: Configure Firebase Cloud Messaging.

Title: Firebase Push Notification

Route: `featureFirebasePushNotification`

Required: 

- Logged in
- Not Stater app (any product plan/license)

Document URL: https://docs.fluxbuilder.com/push-notifications-via-firebase
Firebase Cloud Messaging API (HTTP v1) Document URL: https://docs.fluxbuilder.com/firebase-cloud-messaging-API

Available features for FluxBuilder: Push notification to user app with these
data

- Private Key file (Go to Firebase Console > Firebase application > Project
    settings > Service accounts > Genereate new private key)
- Title
- Message
- Image (optional)
- Dynamic Link (optional)

All Related Configs:

```json
{
    "advanceConfig": {
        "EnableFirebase": false,
    }
}
```

## Firebase Dynamic Link Config (Deprecated)

Description: Configure Firebase Dynamic Links (Deprecated). Use BranchIO Dynamic
Link instead

Title: Firebase Dynamic Link Config (Deprecated)

Route: `featureDynamicLinkConfig`

Compatible Apps:

- All apps except `fluxstore_manager`, `fluxstore_delivery`

Frameworks: 

- `woo`
- `dokan`
- `wcfm`
- `wordpress`
- `shopify`
- `presta`
- `opencart`
- `mylisting`
- `listeo`
- `listpro`
- `bigCommerce`

Document URL: https://docs.fluxbuilder.com/dynamic-link

Include these settings for app: 

- Enable Dynamic Link
- Short Dynamic Link
- Uri Prefix
- Link (Website URL)
- Android Package Name
- Android App Minimum Version
- iOS Bundle ID
- iOS App Minimum Version
- iOS App Store ID

All Related Configs:

```json
{
    "dynamicLinkConfig": {
        "type": "firebase",
        "serviceConfigs": {
        "firebase": {
            "isEnabled": true,
            "shortDynamicLinkEnable": true,
            "uriPrefix": "https://fluxstoreinspireui.page.link",
            "link": "https://mstore.io/",
            "androidPackageName": "com.inspireui.fluxstore",
            "androidAppMinimumVersion": 1,
            "iOSBundleId": "com.inspireui.mstore.flutter",
            "iOSAppMinimumVersion": "1.0.1",
            "iOSAppStoreId": "1469772800"
        },
        "branchIO": {
            "testMode": true,
            "keyTest": "",
            "keyLive": "",
            "liveLinkDomain": "",
            "liveAlternateLinkDomain": "",
            "testLinkDomain": "",
            "testAlternateLinkDomain": ""
        }
        }
    },
}
```

## Firebase Remote Config

Description: Configure Firebase Remote Config. This feature updates both the
Design and Features of App without Republishing App to App Stores (does not
support multilingual).

Title: Firebase Remote Config

Route: `featureFirebaseConfig`

Required (one of the following):

- Or Any User Subcription
- Or Any FluxBuilder Plan
- Or Extended License

Document URL: https://docs.fluxbuilder.com/firebase-remote-config

Include these settings for app:

- Enable Firebase Remote Config (The default fetch interval up to 12 hours)

Available features for FluxBuilder: Upload app settings to Firebase Remote
Config with these data:

- Firebase Config File (Go to Firebase Console > Firebase application >
    Project settings > Service accounts > Genereate new private key)

- Update config > Go to Release History > Click on "Add New Version" Button to upload the latest
  settings to Firebase

All Related Configs:

```json
{
    "enableRemoteConfigFirebase": false,
}
```

## Firebase Analytics

Description: Configure Firebase Analytics.

Title: Firebase Analytics

Route: `featureFirebaseAnalytics`

Required: Enable `featureFirebase` first

Compatible Apps:

- All apps except `fluxstore_manager`, `fluxstore_delivery`, `webapp`

Document URL: https://docs.fluxbuilder.com/google-analytics-facebook-tracking

Include these settings for app:

- Enable Firebase Analytics

All Related Configs:

```json
{
    "enableFirebaseAnalytics": false,
}
```
