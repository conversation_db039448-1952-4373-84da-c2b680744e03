# Payment Methods

All Related Configs:

```json
{
    "loginSetting": {
        "facebookAppId": "430258564493822",
        "facebookLoginProtocolScheme": "fb430258564493822",
    },
}
```

## WebView Payment

Description: Configure WebView-based payment options. Support your local
payments. The payment on App works the same as your Website via WebView.

Title: WebView Payment

Route: `featureOnePageCheckout`

Compatible Apps:

- All apps except `fluxstore_manager`, `fluxstore_delivery`, `fluxnews`, `fluxgpt`, `fluxstore_haravan`, `webapp`

Frameworks: 

- `woo`
- `opencart`
- `wcfm`
- `dokan`
- `shopify`
- `notion`
- `bigCommerce`
- `presta`

Include these settings for app: 

- Enable Webview Checkout: Enable this option so that the app supports all your
  own Payment, Shipping, Checkout plugins via Webview
- Enable Native Checkout: Enable this option so that the app Supports your own
  Payment via Webview. Shipping and Checkout are Native

All Related Configs:

```json
{
    "paymentConfig": {
        "EnableWebviewCheckout": false,
        "EnableNativeCheckout": false,
    },
}
```

## Payment Images

Description: Configure payment method images and icons.

Title: Payment Images

Route: `featurePaymentImages`

Compatible Apps:

- All apps except `fluxstore_manager`, `fluxstore_delivery`, `fluxnews`, `fluxgpt`, `fluxstore_haravan`, `webapp`

Frameworks: 

- `woo`
- `magento`
- `opencart`
- `wcfm`
- `dokan`
- `listeo`
- `presta`
- `notion`

Parameters: 

- `payments`

All Related Configs:

```json
{
    "payments": {
        "stripe_v2_apple_pay": "assets/icons/payment/apple-pay-mark.svg",
        "stripe_v2_google_pay": "assets/icons/payment/google-pay-mark.png",
        "paypal": "assets/icons/payment/paypal.svg",
        "stripe": "assets/icons/payment/stripe.svg",
        "razorpay": "assets/icons/payment/razorpay.svg",
        "tap": "assets/icons/payment/tap.png",
        "paystack": "assets/icons/payment/paystack.png",
        "myfatoorah_v2": "assets/icons/payment/myfatoorah.png",
        "midtrans": "assets/icons/payment/midtrans.png",
        "xendit_cc": "assets/icons/payment/xendit.png",
        "expresspay_apple_pay": "assets/icons/payment/apple-pay-mark.svg",
        "thai-promptpay-easy": "assets/icons/payment/prompt-pay.png",
        "ppcp-gateway": "assets/icons/payment/paypal.svg",
        "thawani_gw": "assets/icons/payment/thawani.png"
    },
}
```

## Razorpay Payment

Description: Configure Razorpay payment gateway.

Title: Razorpay Payment

Route: `featureRazorpay`

Compatible Apps:

- `fluxstore_woo`
- `fluxstore_pro`
- `fluxstore_mv`
- `fluxstore_notion`
- `fluxlisting`

Frameworks: 

- `woo`
- `opencart`
- `wcfm`
- `dokan`
- `notion`
- `listeo`

Parameters: 

- `razorpayConfig`

Include these settings for app: 

- Enable Razorpay
- Key ID
- Key Secret
- Payment Method ID

All Related Configs:

```json
{
    "razorpayConfig": {
        "keyId": "rzp_test_SDo2WKBNQXDk5Y",
        "keySecret": "RrgfT3oxbJdaeHSzvuzaJRZf",
        "paymentMethodId": "razorpay",
        "enabled": true
    },
}
```

## Bank Transfer Payment

Description: Configure Bank Transfer Payment gateway.

Title: Bank Transfer Payment

Route: `featureBankTransferPayment`

Compatible Apps:

- `fluxstore_woo`
- `fluxstore_pro`
- `fluxstore_mv`
- `fluxstore_notion`
- `fluxlisting`
- `fluxstore_prestashop`

Frameworks: 

- `woo`
- `dokan`
- `wcfm`
- `listeo`
- `notion`
- `presta`

Parameters: 

- `bankTransferConfig`

Include these settings for app: 

- Payment Method ID

All Related Configs:

```json
{
    "bankTransferConfig": {
        "paymentMethodIds": ["bacs"],
    },
}
```

## Cash On Delivery Payment

Description: Configure Cash On Delivery Payment gateway.

Title: Cash On Delivery Payment

Route: `featureCashOnDeliveryPayment`

Compatible Apps:

- `fluxstore_woo`
- `fluxstore_pro`
- `fluxstore_mv`
- `fluxstore_notion`
- `fluxlisting`
- `fluxstore_prestashop`

Frameworks: 

- `woo`
- `dokan`
- `wcfm`
- `notion`
- `listeo`
- `opencart`
- `presta`
- `magento`

Parameters: 

- `cashOnDeliveryConfig`

Include these settings for app: 

- Payment Method ID
- Update Order Status: Order status will be updated to "processing" for COD payment method
- Enable Smart COD
    - Extra fee: Only used if enable Smart COD
    - Amount stop: Only used if enable Smart COD

All Related Configs:

```json
{
    "cashOnDeliveryConfig": {
        "paymentMethodIds": ["cod", "cashondelivery"],
    },
    "paymentConfig": {
        "UpdateOrderStatus": true,
        "SmartCOD": {"extraFee": 10, "amountStop": 200, "enabled": true},
    },
}
```

## PayPal Payment

Description: Configure PayPal payment gateway.

Title: PayPal Payment

Route: `featurePaypal`

Compatible Apps:

- `fluxstore_woo`
- `fluxstore_pro`
- `fluxstore_mv`
- `fluxstore_notion`
- `fluxstore_prestashop`
- `fluxlisting`

Frameworks: 

- `woo`
- `magento`
- `dokan`
- `wcfm`
- `notion`
- `listeo`
- `presta`
- `opencart`

Parameters: 

- `paypalConfig`

Document URL: https://docs.fluxbuilder.com/paypal

Include these settings for app: 

- Enable PayPal
- Client ID Key
- Client Secret Key
- Payment Method ID
- Return URL
- Production Mode
- Use Paypal SDK: Use in-app PayPal to handle payment or PayPal SDK to handle
  payment (PayPal SDK is only available for Extended license, Professional
  subscription or AppSumo plan)

All Related Configs:

```json
{
    "paypalConfig": {
        "clientId":
            "ASlpjFreiGp3gggRKo6YzXMyGM6-NwndBAQ707k6z3-WkSSMTPDfEFmNmky6dBX00lik8wKdToWiJj5w",
        "secret":
            "ECbFREri7NFj64FI_9WzS6A0Az2DqNLrVokBo0ZBu4enHZKMKOvX45v9Y1NBPKFr6QJv2KaSp5vk5A1G",
        "returnUrl": "com.inspireui.fluxstore://paypalpay",
        "production": false,
        "paymentMethodId": "paypal",
        "enabled": true,
        "paypalSDKMode": false
    },
}
```

## MercadoPago Payment

Description: Configure MercadoPago payment gateway.

Title: MercadoPago Payment

Route: `featureMercadoPagoPayment`

Compatible Apps:

- `fluxstore_woo`
- `fluxstore_pro`
- `fluxstore_mv`
- `fluxstore_notion`

Frameworks: 

- `woo`
- `dokan`
- `wcfm`
- `notion`

Parameters: 

- `mercadoPagoConfig`

Include these settings for app: 

- Enable
- Payment Method ID
- Production Mode
- Access Token

All Related Configs:

```json
{
    "mercadoPagoConfig": {
        "accessToken":
            "TEST-5726912977510261-102413-65873095dc5b0a877969b7f6ffcceee4-613803978",
        "production": false,
        "paymentMethodId": "woo-mercado-pago-basic",
        "enabled": true
    },
}
```

## Paytm Payment

Description: Configure Paytm payment gateway.

Title: Paytm Payment

Route: `featurePaytmPayment`

Compatible Apps:

- `fluxstore_woo`
- `fluxstore_pro`
- `fluxstore_mv`

Frameworks: 

- `woo`
- `dokan`
- `wcfm`

Document URL: https://docs.fluxbuilder.com/paytm-payment

Include these settings for app: 

- Enable Paytm
- Production Mode
- Payment Method ID
- Merchant Identifier

All Related Configs:

```json
{
    "payTmConfig": {
        "paymentMethodId": "paytm",
        "merchantId": "your-merchant-id",
        "production": false,
        "enabled": true
    },
}
```

## Paystack Payment

Description: Configure Paystack payment gateway.

Title: Paystack Payment

Route: `featurePaystack`

Compatible Apps:

- `fluxstore_woo`
- `fluxstore_pro`
- `fluxstore_mv`

Frameworks: 

- `woo`
- `dokan`
- `wcfm`

Parameters: 

- `payStackConfig`

Document URL: https://docs.fluxbuilder.com/paystack-payment

Include these settings for app: 

- Enable Paystack
- Payment Method ID
- Public Key
- Secret Key
- Enable Mobile Money
- Supported Currencies

All Related Configs:

```json
{
    "payStackConfig": {
        "paymentMethodId": "paystack",
        "publicKey": "pk_test_a1a37615c9ca90dead5dd84dedbb5e476b640a6f",
        "secretKey": "sk_test_d833fcaa6c02a61a9431d2026046c0517888a4a7",
        "supportedCurrencies": ["ZAR"],
        "enableMobileMoney": true,
        "production": false,
        "enabled": true
    },
}
```

## Flutterwave Payment

Description: Configure Flutterwave payment gateway.

Title: Flutterwave Payment

Route: `featureFlutterwave`

Compatible Apps:

- `fluxstore_woo`
- `fluxstore_pro`
- `fluxstore_mv`

Frameworks: 

- `woo`
- `dokan`
- `wcfm`

Parameters: 

- `flutterwaveConfig`

Document URL: https://docs.fluxbuilder.com/flutterwave-payment

Include these settings for app: 

- Enable Flutterwave
- Payment Method ID
- Public Key

All Related Configs:

```json
{
    "flutterwaveConfig": {
        "paymentMethodId": "rave",
        "publicKey": "FLWPUBK_TEST-72b90e0734da8c9e43916adf63cd711e-X",
        "production": false,
        "enabled": true
    },
}
```

## Stripe Payment

Description: Configure Stripe payment gateway.

Title: Stripe Payment

Route: `featureStripe`

Compatible Apps:

- `fluxstore_woo`
- `fluxstore_pro`
- `fluxstore_mv`
- `fluxstore_notion`
- `fluxlisting`

Frameworks: 

- `woo`
- `magento`
- `dokan`
- `wcfm`
- `notion`
- `listeo`

Parameters: 

- `stripeConfig`

Required: Any plan/license that supports VIP features

Document URL: https://docs.fluxbuilder.com/stripe

Include these settings for app: 

- Enable Stripe
- Use Stripe Node Server: Disable if you want to use your own server, enable if
    you want to deploy your Stripe Node Server (`featureDeployStripePayment`)
- Server Endpoint: Only used if enable Use Stripe Node Server
- Publishable Key
- Payment Method IDs
- Return URL
- Enable Manual Capture: Enable automatically captures funds when the
    customer authorizes the payment. Disable will Place a hold on the funds when
    the customer authorizes the payment, but don't capture the funds until later
- Version: V3 support new Ul and Apple Pay/Google Pay. V4 support save cards
- Enable Google Pay
- Enable Apple Pay
- Merchant Identifier: This field is for Stripe payment to use Apple Pay.
- Merchant Display Name
- Merchant Country Code

All Related Configs:

```json
{
    "stripeConfig": {
        "serverEndpoint": "https://stripe-server-node.vercel.app",
        "publishableKey": "pk_test_syl720IY4iwLkNzmOeL7nz3J",
        "paymentMethodIds": ["stripe"],
        "enabled": false,
        "enableApplePay": true,
        "enableGooglePay": true,
        "merchantDisplayName": "FluxStore",
        "merchantIdentifier": "merchant.com.inspireui.mstore.flutter",
        "merchantCountryCode": "US",
        "returnUrl": "fluxstore://inspireui.com",
        "enableManualCapture": false,
        "saveCardAfterCheckout": false,
        "stripeApiVersion": 3
    },
}
```

## Tap Payment

Description: Configure Tap payment gateway.

Title: Tap Payment

Route: `featureTapPayment`

Compatible Apps:

- `fluxstore_woo`
- `fluxstore_pro`
- `fluxstore_mv`
- `fluxstore_notion`
- `fluxlisting`

Frameworks: 

- `woo`
- `opencart`
- `magento`
- `dokan`
- `wcfm`
- `notion`
- `listeo`

Parameters: 

- `tapConfig`

Required: Any plan/license that supports VIP features

Document URL: https://docs.fluxbuilder.com/tap-payment

Include these settings for app: 

- Enable Tap
- Secret Key
- Payment Method ID

All Related Configs:

```json
{
    "tapConfig": {
        "SecretKey": "sk_test_XKokBfNWv6FIYuTMg5sLPjhJ",
        "paymentMethodId": "tap",
        "enabled": false
    },
}
```

## MyFatoorah Payment

Description: Configure MyFatoorah payment gateway.

Title: MyFatoorah Payment

Route: `featureMyFatoorah`

Compatible Apps:

- `fluxstore_woo`
- `fluxstore_pro`
- `fluxstore_mv`
- `fluxlisting`

Frameworks: 

- `woo`
- `dokan`
- `wcfm`
- `listeo`

Parameters: 

- `myFatoorahConfig`

Required: Any plan/license that supports VIP features

Include these settings for app: 

- Enable MyFatoorah
- Production Mode
- Payment Method ID
- API Token

All Related Configs:

```json
{
    "myFatoorahConfig": {
        "paymentMethodId": "myfatoorah_v2",
        "apiToken":
            "rLtt6JWvbUHDDhsZnfpAhpYk4dxYDQkbcPTyGaKp2TYqQgG7FGZ5Th_WD53Oq8Ebz6A53njUoo1w3pjU1D4vs_ZMqFiz_j0urb_BH9Oq9VZoKFoJEDAbRZepGcQanImyYrry7Kt6MnMdgfG5jn4HngWoRdKduNNyP4kzcp3mRv7x00ahkm9LAK7ZRieg7k1PDAnBIOG3EyVSJ5kK4WLMvYr7sCwHbHcu4A5WwelxYK0GMJy37bNAarSJDFQsJ2ZvJjvMDmfWwDVFEVe_5tOomfVNt6bOg9mexbGjMrnHBnKnZR1vQbBtQieDlQepzTZMuQrSuKn-t5XZM7V6fCW7oP-uXGX-sMOajeX65JOf6XVpk29DP6ro8WTAflCDANC193yof8-f5_EYY-3hXhJj7RBXmizDpneEQDSaSz5sFk0sV5qPcARJ9zGG73vuGFyenjPPmtDtXtpx35A-BVcOSBYVIWe9kndG3nclfefjKEuZ3m4jL9Gg1h2JBvmXSMYiZtp9MR5I6pvbvylU_PP5xJFSjVTIz7IQSjcVGO41npnwIxRXNRxFOdIUHn0tjQ-7LwvEcTXyPsHXcMD8WtgBh-wxR8aKX7WPSsT1O8d8reb2aR7K3rkV3K82K_0OgawImEpwSvp9MNKynEAJQS6ZHe_J_l77652xwPNxMRTMASk1ZsJL",
        "accountCountry": "KW",
        "production": false,
        "enabled": false
    },
}
```

## In-App Purchase

Description: Configure in-app purchase functionality.

Title: In App Purchase

Route: `featureInAppPurchaseConfig`

Compatible Apps:

- `fluxstore_woo`
- `fluxstore_pro`
- `fluxstore_mv`

Frameworks: 

- `woo`
- `dokan`
- `wcfm`

Required: Any plan/license that supports VIP features

Include these settings for app: 

- Enable In App Purchase
- Consumable product ids
- Non consumable product ids
- Subscription product ids

All Related Configs:

```json
{
    "inAppPurchaseConfig": {
        "consumableProductIDs": ["com.inspireui.fluxstore.test"],
        "nonConsumableProductIDs": [],
        "subscriptionProductIDs": ["com.inspireui.fluxstore.subscription.test"],
        "enabled": false
    },
}
```

## Midtrans Payment

Description: Configure Midtrans payment gateway.

Title: Midtrans Payment

Route: `featureMidtrans`

Compatible Apps:

- `fluxstore_woo`
- `fluxstore_pro`
- `fluxstore_mv`

Frameworks: 

- `woo`
- `dokan`
- `wcfm`

Parameters: 

- `midtransConfig`

Required: Any plan/license that supports VIP features

Include these settings for app: 

- Enable Midtrans
- Payment Method ID
- Client key

All Related Configs:

```json
{
    "midtransConfig": {
        "paymentMethodId": "midtrans",
        "clientKey": "SB-Mid-client-he8W_FIlvugfA2RD",
        "enabled": false
    },
}
```

## Shopify Payment

Description: Configure Shopify payment gateway.

Title: Shopify Payment

Route: `featureShopifyPayment`

Compatible Apps:

- `fluxstore_shopify`

Parameters: 

- `shopifyPaymentConfig`

Required: Any plan/license that supports VIP features

Document URL: https://docs.fluxbuilder.com/shopify-payment

Include these settings for app: 

- Common settings:
    - Production Mode
    - Shop Name
    - Country Code
- Stripe Payment Card: Go to Deploy Stripe Payment to get endpoint (`featureDeployStripePayment`)
    - Enable
    - Endpoint: The URL of the server domain
- Google Pay:
    - Enable: Stripe pushlish key
    - Key: Merchant ID
- Apple Pay: 
    - Enable: Merchant ID

All Related Configs:

```json
{
    "shopifyPaymentConfig": {
        "shopName": "FluxStore",
        "countryCode": "US",
        "productionMode": false,
        "paymentCardConfig": {
        "enable": false,
        "serverEndpoint": "https://test-stripe-nine.vercel.app",
        },
        "applePayConfig": {
        "enable": false,
        "merchantId": "merchant.com.inspireui.fluxstore",
        },
        "googlePayConfig": {
        "enable": false,
        "stripePublishableKey": "pk_test_O3awus9i5mA2wIX9a7pU3MSi00gZPcpJWX",
        "merchantId": "merchant.com.inspireui.fluxstore"
        },
    },
}
```

## Xendit Payment

Description: Configure Xendit payment gateway.

Title: Xendit Payment

Route: `featureXenditPayment`

Compatible Apps:

- `fluxstore_woo`
- `fluxstore_pro`
- `fluxstore_mv`

Frameworks: 

- `woo`
- `dokan`
- `wcfm`

Parameters: 

- `xenditConfig`

Required: Any plan/license that supports VIP features

Include these settings for app: 

- Enable Xendit Payment
- Payment Method ID
- Secret Api key

All Related Configs:

```json
{
    "xenditConfig": {
        "paymentMethodId": "xendit",
        "secretApiKey":
            "xnd_development_4E9ql5zFiC1BBmhK2r7wr9mNYyyvjLs0fIal00tGuHEj1iEYCu7B7tCUudv3Xe",
        "enabled": false
    },
}
```

## Thawani Payment

Description: Configure Thawani payment gateway.

Title: Thawani Payment

Route: `featureThawaniPayment`

Compatible Apps:

- `fluxstore_woo`
- `fluxstore_pro`
- `fluxstore_mv`

Frameworks: 

- `woo`
- `dokan`
- `wcfm`
- `opencart`

Parameters: 

- `thawaniConfig`

Required: Any plan/license that supports VIP features

Include these settings for app: 

- Enable Thawani Payment
- Production Mode
- Payment Method ID
- Secret Key
- Publishable Key

All Related Configs:

```json
{
    "thawaniConfig": {
        "paymentMethodId": "thawani_gw",
        "secretKey": "rRQ26GcsZzoEhbrP2HZvLYDbn9C9et",
        "publishableKey": "HGvTMLDssJghr9tlN9gr4DVYt0qyBy",
        "production": false,
        "enabled": true
    },
}
```

## PayPal Express Payment

Description: Configure PayPal Express payment gateway.

Title: PayPal Express Payment

Route: `featurePaypalExpressPayment`

Compatible Apps:

- `fluxstore_pro`

Frameworks: 

- `magento`

Parameters: 

- `paypalExpressConfig`

Include these settings for app: 

- Enable PayPal
- Production Mode
- Payment Method ID
- Username
- Password
- Signature
- Payment Action

All Related Configs:

```json
{
    "paypalExpressConfig": {
        "username": "sb-wea3q30917031_api1.business.example.com",
        "password": "9MN73T4JHTBDY5W7",
        "signature": "A-X91d6dvj07IIDTUn5hM8p8w8LxA-5D.cnvNUgufzpxxf1NNZBYh3kq",
        "paymentAction": "Sale", //Sale, Order, Authorization.
        "production": false,
        "paymentMethodId": "paypal_express",
        "enabled": false,
    },
}
```

## FIB Payment

Description: Configure First Iraqi Bank payment gateway.

Title: First Iraqi Bank Payment

Route: `featureFIBPayment`

Compatible Apps:

- `fluxstore_woo`
- `fluxstore_pro`
- `fluxstore_mv`

Frameworks: 

- `woo`
- `dokan`
- `wcfm`

Parameters: 

- `fibConfig`

Required: Any plan/license that supports VIP features

Include these settings for app: 

- Enable
- Payment Method ID
- Merchant Key
- Merchant ID

All Related Configs:

```json
{
    "fibConfig": {
        "paymentMethodId": "fib",
        "clientId": "narin-beauty",
        "clientSecret": "7ffcd642-87b7-4cc0-b75d-c25d5276cffe",
        "mode": "stage", // stage - dev - prod or any other mode
        "enabled": false
    },
}
```

## Express Pay

Description: Configure express payment features.

Title: Express Pay

Route: `featureExpressPay`

Compatible Apps:

- `fluxstore_woo`
- `fluxstore_pro`
- `fluxstore_mv`

Frameworks: 

- `woo`
- `dokan`
- `wcfm`

Required (all of the following):

- `extended`
- `source`

Support Agency: `false`
- Enable
- Production
- Payment Method ID
- Merchant Key
- Merchant ID
- Merchant Password

All Related Configs:

```json
{
    "expressPayConfig": {
        "paymentMethodId": "shahbandrpay",
        "merchantKey": "b2be2ffc-c8b9-11ed-82a9-42eb4e39c8ae",
        "merchantPassword": "4a00a5fd3c63dd2b743c75746af6ffe2",
        "merchantId": "merchant.com.inspireui.mstore.flutter",
        "production": false,
        "enabled": true
    },
}
```

## Thai PromptPay

Description: Configure Thai PromptPay payment system.

Title: Thai PromptPay

Route: `featurePromptPay`

Compatible Apps:

- `fluxstore_woo`
- `fluxstore_pro`
- `fluxstore_mv`
- `fluxlisting`

Frameworks: 

- `woo`
- `dokan`
- `wcfm`
- `listeo`

Required: 

- `extended`
- `source`

Support Agency: `false`

Include these settings for app: 

- Enable
- Payment Method ID

All Related Configs:

```json
{
    "thaiPromptPayConfig": {
        "paymentMethodId": "thai-promptpay-easy",
        "enabled": false
    },
}
```

## PhonePe PromptPay

Description: Configure PhonePe payment system.

Title: PhonePe

Route: `featurePhonePePayment`

Compatible Apps:

- `fluxstore_woo`
- `fluxstore_pro`
- `fluxstore_mv`

Frameworks: 

- `woo`
- `dokan`
- `wcfm`

Include these settings for app: 

- Enable PhonePe
- Production Mode
- Payment Method IDs
- Merchant Id
- Salt Key
- Salt Key Index
- Android Package Name
- iOS Bundle Id

All Related Configs:

```json
{
    "phonepeConfig": {
        "paymentMethodIds": ["phonepe"],
        "merchantId": "GANGSTARPGUAT",
        "saltKey": "7eb940c6-b785-42e2-98ca-0419ebf5a219",
        "saltKeyIndex": "1",
        "androidPackageName": "com.inspireui.fluxstore",
        "iOSBundleId": "com.inspireui.mstore.flutter",
        "production": false,
        "enabled": true
    },
}
```
